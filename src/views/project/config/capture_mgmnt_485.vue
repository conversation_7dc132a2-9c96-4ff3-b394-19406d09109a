<template>
  <div v-if="newItem">
    <a-row :gutter="20" style="margin-top: 10px">
      <a-col style="line-height: 28px; height: 28px;"> 更换串口: </a-col>
      <a-col style="width: 240px">
        <a-select v-model:value="portNoChange">
          <template v-for="item in portNoList" :key="item">
            <a-select-option :value="item.value">
              {{ item.label }}
            </a-select-option>
          </template>
        </a-select>
      </a-col>
      <a-col>
        <a-button type="primary" @click="addModalVisible = true" style="margin-left: 10px">新增</a-button>
        <a-button type="primary" @click="changedPortNo" style="margin-left: 10px">更换串口</a-button>
        <a-button type="primary" style="margin-left: 10px;" @click="confirmDevice">完成</a-button>
        <a-button type="primary" style="margin-left: 10px;" @click="refresh">刷新</a-button>
        <a-button type="danger" style="margin-left: 10px;" @click="deleteDevice">删除</a-button>
      </a-col>
    </a-row>
    <a-modal v-model:visible="addModalVisible" title="新增设备" width="800px" @ok="addTable">
      <a-row :gutter="20">
        <a-col style="line-height: 28px; height: 28px; width: 130px; text-align: right">
          选择产品:
        </a-col>
        <a-col style="width: 240px">
          <a-button type="primary" @click="productShowModal">
            点击选择产品
          </a-button>
          <a-modal v-model:visible="productVisible" title="项目产品选择" width="1000px">
            <template #footer>
              <a-button key="back" @click="productHandleCancel">关闭</a-button>
            </template>
            <div style="margin-bottom: 10px">
              <a-input v-model:value="searchValue.sysName" placeholder="请输入所属系统" style="width: 200px;margin-right: 10px"
                allow-clear />
              <a-input v-model:value="searchValue.brandName" placeholder="请输入所属品牌"
                style="width: 200px;margin-right: 10px" allow-clear />
              <a-input v-model:value="searchValue.modelName" placeholder="请输入所属类型"
                style="width: 200px;margin-right: 10px" allow-clear />
              <a-button type="primary" @click="searchProduct">搜索</a-button>
            </div>
            <a-table :columns="proColumns" :data-source="productOptions" bordered :pagination="false"
              :scroll="{ y: 500 }" :size="'small'">
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'operation'">
                  <div class="editable-row-operations">
                    <a type="link" @click="productSelect(record)">选择</a>
                  </div>
                </template>
                <template v-else>
                  {{ text }}
                </template>
              </template>
            </a-table>
            <div class="pagination">
              <Pagination :page="page" :size="size" :total="total" @pageSizeChange="pageSizeChange"
                @pageChange="pageChange" />
            </div>
          </a-modal>
        </a-col>
        <a-col style="line-height: 28px; height: 28px; width: 130px; text-align: right">
          已选产品:
        </a-col>
        <a-col style="width: 240px">
          {{ productSelected }}
        </a-col>
      </a-row>
      <a-row :gutter="20" style="margin-top: 10px">
        <a-col style="line-height: 28px; height: 28px; width: 130px; text-align: right">
          设备名称:
        </a-col>
        <a-col style="width: 240px">
          <a-input v-model:value="newItem.name" />
        </a-col>
        <a-col style="width: 130px;text-align: right"> 设备简称: </a-col>
        <a-col style="width: 240px">
          <a-input v-model:value="newItem.deviceShortName" placeholder="输入英文字母和数字" />
        </a-col>
      </a-row>
      <a-row :gutter="20" style="margin-top: 10px">
        <a-col style="line-height: 28px; height: 28px; width: 130px; text-align: right">
          通讯协议:
        </a-col>
        <a-col style="width: 240px">
          <a-select v-model:value="newItem.protocolType">
            <template v-for="item in protocolTypeOptions" :key="item">
              <a-select-option :value="item.value">
                {{ item.value }}
              </a-select-option>
            </template>
          </a-select>
        </a-col>
        <a-col style="width: 130px;text-align: right"> 超时时间(毫秒): </a-col>
        <a-col style="width: 240px">
          <a-input v-model:value="newItem.comm.txTimeout" />
        </a-col>
      </a-row>
      <template v-if="portNo < 17 || portNo > 18">
        <a-row :gutter="20" style="margin-top: 10px">
          <a-col style="width: 130px;text-align: right"> 协议设置: </a-col>
          <a-col style="width: 240px">
            <a-input v-model:value="newItem.comm.serialPortSetting" />
          </a-col>
          <a-col style="width: 130px;text-align: right"> 通讯地址(十进制): </a-col>
          <a-col style="width: 240px">
            <a-input v-model:value="newItem.comm.serialPortAddress" />
          </a-col>
        </a-row>
      </template>
      <template v-if="portNo >= 17">
        <a-row :gutter="20" style="margin-top: 10px">
          <a-col style="width: 130px;text-align: right">
            IP地址:
          </a-col>
          <a-col style="width: 240px">
            <a-input v-model:value="newItem.comm.tcpIP" />
          </a-col>
          <a-col style="width: 130px;text-align: right">
            端口:
          </a-col>
          <a-col style="width: 240px">
            <a-input v-model:value="newItem.comm.tcpPort" />
          </a-col>
        </a-row>
        <a-row :gutter="20" style="margin-top: 10px">
          <a-col style="width: 130px;text-align: right"> FSU Device ID: </a-col>
          <a-col style="width: 240px">
            <a-input v-model:value="newItem.comm.fsuDeviceId" />
          </a-col>
          <a-col style="width: 130px;text-align: right"> Modbus地址: </a-col>
          <a-col style="width: 240px">
            <a-input v-model:value="newItem.comm.modbusAddress" />
          </a-col>
        </a-row>
      </template>
      <a-row :gutter="20" style="margin-top: 10px">
        <a-col style="width: 130px;text-align: right"> 设备数量: </a-col>
        <a-col style="width: 240px">
          <a-input v-model:value="deviceCount" />
        </a-col>
        <a-col style="width: 130px;text-align: right"> 结束符: </a-col>
        <a-col style="width: 240px">
          <a-input v-model:value="newItem.txEnd" />
        </a-col>
      </a-row>
      <a-row :gutter="20" style="margin-top: 10px">
        <a-col style="width: 130px;text-align: right"> 所属机房: </a-col>
        <a-col :span="19">
          <a-tree-select v-model:value="newItem.deviceLocationId" style="width: 100%" placeholder="请选择地址" allow-clear
            tree-default-expand-all :tree-data="treeData" treeLine :field-names="{
              children: 'children',
              label: 'addressName',
              key: 'addressCode',
              value: 'addressCode',
            }"></a-tree-select>
        </a-col>
      </a-row>
      <a-row :gutter="20" style="margin-top: 10px"> 
        <a-col style="width: 130px;text-align: right"> 产品参数同步: </a-col>
        <a-col style="width: 240px">
          <a-radio-group v-model:value="newItem.productSync">
            <a-radio value="0">否</a-radio>
            <a-radio value="1">是</a-radio>
          </a-radio-group>
        </a-col>
        <a-col style="width: 130px;text-align: right"> 实际设备地址: </a-col>
        <a-col style="width: 240px">
          {{ realDeviceAddressString }}
        </a-col>
      </a-row>
    </a-modal>
    <div style="margin-top: 20px">
      <a-table :dataSource="dataSource" :columns="columns" :scroll="{ y: scrollHight }" :size="'small'"
        :pagination="false" :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
        :row-key="record => record.id">
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="column.dataIndex === 'operation'">
            <a style="margin-left: 20px" @click="import_data(record)">
              导入参数
            </a>
            <a style="margin-left: 10px" @click="showModal(record)">编辑</a>
          </template>
          <template v-else-if="column.dataIndex === 'existRules'">
            <a-tag v-if="record.existRules == 0" color="red">否</a-tag>
            <a-tag v-else color="green">是</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'completion'">
            <a-tag v-if="record.completion == 0" color="red">否</a-tag>
            <a-tag v-else color="green">是</a-tag>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
      <div style="margin-top: 20px">
        <Pagination :page="listPage" :size="listSize" :total="listTotal" @pageSizeChange="listPageSizeChange"
          @pageChange="listPageChange" />
      </div>
    </div>
    <a-modal v-model:visible="visible" title="编辑参数" :confirm-loading="confirmLoading" @ok="handleOk">
      <a-form ref="formRef" :model="formState" name="basic" autocomplete="off" :label-col="labelCol">
        <a-form-item label="设备名称" name="deviceName" :rules="[{ required: true, message: '请输入设备名称!' }]">
          <a-input v-model:value="formState.deviceName" />
        </a-form-item>
        <a-form-item label="协议设置" name="comSetting" :rules="[{ required: true, message: '请输入协议设置!' }]">
          <a-input v-model:value="formState.comSetting" style="width: 100%" />
        </a-form-item>
        <a-form-item label="设备简称" name="deviceShortName">
          <a-input v-model:value="formState.deviceShortName" style="width: 100%" />
        </a-form-item>
        <a-form-item label="物理地址" name="originalAddress">
          <a-input v-model:value="formState.originalAddress" style="width: 100%" />
        </a-form-item>
        <template v-if="portNo >= 17">
          <a-form-item label="Modbus" name="modbusAddress">
            <a-input v-model:value="formState.modbusAddress" style="width: 100%" />
          </a-form-item>
          <a-form-item label="通讯协议:" name="comType" :rules="[{ required: true }]">
            <a-select v-model:value="formState.comType">
              <template v-for="item in protocolTypeOptions" :key="item">
                <a-select-option :value="item.value">
                  {{ item.value }}
                </a-select-option>
              </template>
            </a-select>
          </a-form-item>
        </template>
        <a-form-item>
          所属机房：
          <a-tree-select v-model:value="formState.deviceLocationId" style="width: 100%" placeholder="请选择地址" allow-clear
            tree-default-expand-all :tree-data="treeData" treeLine :field-names="{
              children: 'children',
              label: 'addressName',
              key: 'addressCode',
              value: 'addressCode',
            }"></a-tree-select>
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal v-model:visible="portNoChangeVisible" title="更换串口进度信息" @ok="portNoChangeVisible = false">
      <div style="height: 50vh; overflow: scroll; background: white; padding: 5px" id="log">
        <div v-for="(log, idx) in logs" v-bind:key="'log_' + idx" style="color: black">
          {{ log }}
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
var XLSX = require('xlsx')
import Pagination from '../../../components/pagination.vue'
import { addDevice, getDeviceList,deleteDevice, updatePortNo, updateCompletion, uploadDeviceAttributes } from '../../../api/devices.js'
import { getProjectProducts, locationsTree } from '../../../api/projects.js'
import { doEdit } from '../../../api/dataDeal.js'
import { message } from 'ant-design-vue'
import { uuid } from '../../../utils'
// 导入参数转换
import { convertExcelToStandard } from '../../../config/parameterMapping'
const ip = require('ip')
export default {
  components: { Pagination },
  name: 'capture_mgmnt_485',
  props: ['gw', 'portNo'],
  data() {
    return {
      addModalVisible: false,
      state: {
        selectedRowKeys: [],
      },
      searchValue: {
        sysName: '',
        brandName: '',
        modelName: '',
      },
      productVisible: false,
      checked: false,
      logs: [],
      labelCol: {
        style: {
          width: '80px',
        },
      },
      proColumns: [
        {
          title: '所属系统',
          dataIndex: 'sysName',
        },
        {
          title: '设备类型',
          dataIndex: 'deviceTypeName',
        },
        {
          title: '所属品牌',
          dataIndex: 'brandName',
        },
        {
          title: '型号名称',
          dataIndex: 'modelName',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          width: '80px',
        },
      ],
      total: 0,
      size: 10,
      page: 1,
      productSelected: '',
      productOptions: null,
      columns: [
        {
          title: '编号',
          dataIndex: 'deviceCode',
          key: 'deviceCode',
        },
        {
          title: '设备名称',
          dataIndex: 'deviceName',
          key: 'deviceName',
        },
        {
          title: '所属品牌',
          dataIndex: 'brandName',
          key: 'brandName',
          width: '100px',
        },
        {
          title: '设备类型',
          dataIndex: 'deviceTypeName',
          key: 'deviceTypeName',
          width: '100px',
        },
        {
          title: '型号名称',
          dataIndex: 'modelName',
          key: 'modelName',
        },
        {
          title: '协议类型',
          dataIndex: 'comType',
          key: 'comType',
        },
        {
          title: '物理地址',
          dataIndex: 'originalAddress',
          key: 'originalAddress',
          width: '100px',
        },
        {
          title: '所属机房',
          dataIndex: 'addressName',
          key: 'addressName',
        },
        {
          title: '有参数?',
          dataIndex: 'existRules',
          key: 'existRules',
          width: '80px',
        },
        {
          title: '完成?',
          dataIndex: 'completion',
          key: 'completion',
          width: '80px',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          width: '200px',
        },
      ],
      deviceProtocol: '',
      portNoList: [],
      portNoChange: 1,
      portNoChangeVisible: false,
      protocolTypeOptions: [
        {
          value: 'Modbus-RTU',
        },
        {
          value: 'Modbus-ASCII',
        },
        {
          value: 'Modbus-TCP',
        },
        {
          value: 'SNMP',
        },
        {
          value: 'Dev-DO',
        },
        {
          value: 'Dev-DI',
        },
        {
          value: 'HTTP',
        },
        {
          value: 'BacNet',
        },
        {
          value: 'B',
        },
        {
          value: 'MQTT_ShenShan_Building6_BA',
        },
        {
          value: 'MQTT_ShenShan_Building4',
        },
      ],
      deviceCount: 1,
      newItem: {
        name: '',
        protocolType: 'Modbus-RTU',
        extra: '',
        originalAddress: '',
        deviceLocationId: '',
        projectId: '',
        txEnd: '<JBUS>',
        deviceShortName: '',
        productSync: '1',
        comm: {
          dataType: 'HEX',
          endType: '新行+回车',
          serialPort: '/dev/tty.usbserial-00000001',
          serialPortSetting: '9600,n,8,1',
          serialPortAddress: '1',
          txTimeout: 5000,
          tcpIP: '',
          tcpPort: '',
          udpAddress: '',
          udpPort: '',
          snmpAgentIP: '',
          snmpAgentPort: '',
          snmpAgentGroup: 'public',
          fsuDeviceId: '',
          modbusAddress: '',
        },
      },
      dataSource: [],
      getProductLoading: false,
      scrollHight: 0,
      visible: false,
      confirmLoading: false,
      formState: {
        portNo: '',
        deviceName: '',
        originalAddress: '',
        tcpIP: '',
        tcpPort: '',
        comType: '',
        deviceLocationId: '',
        comSetting: '',
        deviceShortName: '',
        modbusAddress: '',
      },
      formRef: null,
      editableData: {},
      treeData: [],
      serialPortType: this.gw.serialPortType,
      listPage: 1,
      listSize: 10,
      listTotal: 0,
    }
  },
  computed: {
    realDeviceAddressString() {
      if (this.newItem === undefined || this.newItem.comm === undefined) {
        return ''
      }
      let ll = [
        parseInt(this.newItem.comm.serialPortAddress),
        parseInt(this.newItem.comm.serialPortAddress) +
        parseInt(this.deviceCount) -
        1,
      ]
      if (ll[0] === ll[1]) {
        return ll[0]
      }
      return ll.join('~')
    },
  },
  created() {
    let model = parseInt(this.gw.model)
    this.portNoList = [...Array(model)].map((_, i) => ({
      value: i + 1,
      label: `串口${i + 1}`,
    }))
    this.newItem.deviceLocationId = this.gw.deviceLocationId
    this.getAddressList()
    if (this.gw.serialPortType) {
      this.serialPortType = this.gw.serialPortType
    } else {
      this.serialPortType = 'ttyWCH'
    }
  },
  mounted() {
    let o = document.getElementById('vab-content')
    let h = o.clientHeight || o.offsetHeight
    this.scrollHight = h - 280
  },
  methods: {
    onSelectChange(selectedRowKeys) {
      console.log(selectedRowKeys)
      this.state.selectedRowKeys = selectedRowKeys
    },
    // 刷新
    refresh() {
      this.getProjectDeviceMth()
    },
    // 删除设备
    deleteDevice() {
      if (this.state.selectedRowKeys.length == 0) {
        message.warning('请选择要删除的设备')
        return
      }
      deleteDevice({ deviceIds: this.state.selectedRowKeys }).then(res => {
        message.success('删除成功')
        this.getProjectDeviceMth()
      })
    },
    // 确认完成
    confirmDevice() {
      updateCompletion({ ids: this.state.selectedRowKeys }).then(() => {
        message.success('确认完成')
        this.getProjectDeviceMth()
      })
    },
    // 搜索
    searchProduct() {
      this.getProjectProductListMth()
    },
    //  分页
    pageChange(Page) {
      this.page = Page
      this.getProjectProductListMth()
    },
    // 分页
    pageSizeChange(Page, pageSize) {
      this.page = Page
      this.size = pageSize
    },
    // 打开产品弹窗
    productShowModal() {
      this.getProjectProductListMth()
    },
    // 关闭产品弹窗
    productHandleCancel() {
      this.productVisible = false
    },
    // 写入日记
    appendLog(log) {
      this.logs.push(log)
      const elem = document.getElementById('log')
      if (elem) {
        elem.scrollTop = elem.scrollHeight
      }
    },
    // 更换串口
    async changedPortNo() {
      if (this.portNo == this.portNoChange) {
        message.warning('串口不能和原来的一样')
        return
      }
      updatePortNo({ ids: this.state.selectedRowKeys, portNo: this.portNoChange }).then(() => {
        message.success('更改成功')
        this.getProjectDeviceMth()
      })
    },
    // 获取地址数据
    getAddressList() {
      if (this.gw.projectId) {
        locationsTree(this.gw.projectId).then((res) => {
          this.treeData = res
          this.getProjectDeviceMth()
        })
      }
    },
    // 打开模态框
    showModal(data) {
      this.visible = true
      this.editableData = data
      this.formState = {
        deviceName: data.deviceName,
        originalAddress: data.originalAddress,
        deviceLocationId: data.deviceLocationId,
        comType: data.comType,
        deviceId: data.id,
        deviceLocationPathId: data.deviceLocationPathId,
        comSetting: data.comSetting,
        deviceShortName: data.deviceShortName,
        modbusAddress: data.modbusAddress,
      }
    },
    // 确认编辑
    handleOk() {
      this.$refs['formRef'].validate().then(() => {
        this.confirmLoading = true
        if (
          this.formState.comSetting.includes(`/dev/${this.serialPortType}`)
        ) {
          let serialPortSetting =
            this.formState.comSetting.match(/,(\S*)/)[1]
          this.formState.comSetting =
            `/dev/${this.serialPortType}` +
            (this.formState.portNo - 1) +
            ',' +
            serialPortSetting
        }
        doEdit('project_devices', this.formState.deviceId, {
          deviceName: this.formState.deviceName,
          originalAddress: this.formState.originalAddress,
          deviceLocationId: this.formState.deviceLocationId,
          deviceShortName: this.formState.deviceShortName,
          modbusAddress: this.formState.modbusAddress,
          comSetting: this.formState.comSetting,
          comType: this.formState.comType,
        }).then(
          (res) => {
            message.success('修改成功')
            this.getProjectDeviceMth()
          }
        )
        this.visible = false
        this.confirmLoading = false
      })
    },
    // 导入
    import_data(record) {
      let input = document.createElement('input')
      input.type = 'file'
      input.click()
      input.onchange = ($event) => {
        this.parse_imported_data($event, record)
      }
    },
    parse_imported_data(e, record) {
      let that = this
      // 读取表格文件
      let fileName = ''
      const files = e.target.files
      if (files.length <= 0) {
        message.warning('请上传文件')
        return false
      } else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
        message.warning('上传格式不正确,请上传xls或者xlsx格式')
        return false
      } else {
        // 更新获取文件名
        fileName = files[0].name
      }
      const fileReader = new FileReader()
      fileReader.onload = async (ev) => {
          let workbook = XLSX.read(ev.target.result, {
            type: 'binary',
          })
          await that.process_productSheet(workbook, record.id)
      }
      fileReader.readAsArrayBuffer(files[0])
    },
    // 处理产品数据
    async process_productSheet(workbook, deviceId) {
      let rules = []
      let ws = XLSX.utils.sheet_to_json(workbook.Sheets.Devvou, { 
        defval: '',
        raw: false
      })
      
      if (ws) {
        for (const element of ws) {
          rules.push(convertExcelToStandard(element))
        }
      }
      await uploadDeviceAttributes({ deviceId: deviceId, rules })
      message.success('导入成功')
      this.getProjectDeviceMth()
    },
    // 产品选择
    productSelect(value) {
      let res = value
      this.newItem.productId = res.productId
      this.productSelected = `${res.brandName}-${res.modelName}-${res.deviceTypeName}`
      this.productVisible = false
    },
    // 获取设备信息
    getProjectDeviceMth() {
      getDeviceList({ page: this.listPage, limit: this.listSize, gatewayId: this.$route.query.gatewayId, where: { portNo: this.portNo } }).then((res) => {
        const { data, pagination } = res
        this.dataSource = data
        this.listTotal = pagination.total
        this.state.selectedRowKeys = []
      })
    },
    // 获取设备信息分页
    listPageChange(page) {
      this.listPage = page
      this.getProjectDeviceMth()
    },
    // 获取设备信息分页
    listPageSizeChange(page, size) {
      this.listPage = page
      this.listSize = size
    },
    async onChange($event, item) {
      let completion = $event.target.checked
      await editProjectDevice(item.key, '', { completion })
    },
    // 获取项目中已经添加的产品
    getProjectProductListMth() {
      this.productVisible = true
      let params = {
        limit: this.size,
        page: this.page,
        where: {}
      }
      if (this.searchValue.sysName) {
        params.where.sysName = this.searchValue.sysName
      }
      if (this.searchValue.brandName) {
        params.where.brandName = this.searchValue.brandName
      }
      if (this.searchValue.modelName) {
        params.where.modelName = this.searchValue.modelName
      }
      getProjectProducts(this.gw.projectId, params).then((res) => {
        const { data, pagination } = res
        this.total = pagination.total
        this.productOptions = data
      })
    },
    // 添加数据到表格中
    async addTable() {
      try {
        for (var i = 0; i < this.deviceCount; i++) {
          let deviceName = this.newItem.name + `-${i + 1}`
          let originalAddress = ''
          if (this.portNo < 17 || this.portNo > 18) {
            originalAddress = `${i + parseInt(this.newItem.comm.serialPortAddress)}`
          } else {
            originalAddress = ip.toLong(this.newItem.comm.tcpIP)
            if (this.newItem.protocolType == 'B') {
              originalAddress = `${this.newItem.comm.tcpIP}:${this.newItem.comm.tcpPort}:${this.newItem.comm.fsuDeviceId}`
            } else {
              originalAddress = `${this.newItem.comm.tcpIP}:${this.newItem.comm.tcpPort}`
            }
          }
          let deviceShortName = ''
          let modbusAddress = ''
          if (this.newItem.comm.modbusAddress) {
            modbusAddress = this.newItem.comm.modbusAddress
          }
          if (this.newItem.deviceShortName) {
            deviceShortName = this.newItem.deviceShortName
          }
          let deviceCode = `SB${uuid(10)}`
          let params = {
            gatewayId: this.$route.query.gatewayId,
            portNo: this.portNo,
            deviceName,
            deviceCode,
            productId: this.newItem.productId,
            comType: this.newItem.protocolType,
            comDataType: 'HEX',
            comSetting:
              `/dev/${this.serialPortType}` +
              (this.portNo - 1) +
              ',' +
              this.newItem.comm.serialPortSetting,
            originalAddress,
            deviceLocationId: this.newItem.deviceLocationId,
            device2dXy: 'device2DXY',
            device3dXyz: 'device3DXYZ',
            txEnd: this.newItem.txEnd,
            deviceShortName,
            modbusAddress,
            productSync: this.newItem.productSync,
          }
          if (
            [
              'TCP',
              'SNMP',
              'MQTT_ShenShan_Building6_BA',
              'MQTT_ShenShan_Building4',
            ].includes(params.comType)
          ) {
            params.comSetting =
              this.newItem.comm.tcpIP + ':' + this.newItem.comm.tcpPort
          }
          await addDevice(params)
        }
        this.addModalVisible = false
        message.success('添加成功')
        this.getProjectDeviceMth()
      } catch (error) {
        console.log(error)
      }
    },
    onDelete(key) {
      delProjectDevice(key).then((_res) => {
        this.getProjectDeviceMth()
      })
    },
    systemSelected(value) {
      getDeviceType({
        system: value,
      }).then((res) => {
        let data = []
        let list = res
        for (let item of list) {
          data.push({
            label: item.attributes.name,
            value: item.attributes.name,
          })
        }
        this.deviceTypeOptions = data
      })
    },
    deviceTypeSelected(value) {
      getBrandList({
        deviceType: value,
      }).then((res) => {
        let data = []
        let list = res
        for (let item of list) {
          data.push({
            label: item.attributes.name,
            value: item.attributes.name,
          })
        }
        this.brandOptions = data
      })
    },
    brandSelected(value) {
      getModelList({
        brand: value,
      }).then((res) => {
        let data = []
        let list = res
        for (let item of list) {
          data.push({
            label: item.attributes.name,
            value: item.attributes.name,
          })
        }
        this.modelOptions = data
      })
    },
  },
}
</script>
<style scoped lang="less">
.ant-select {
  width: 100%;
}

// Add consistent padding and margin to form elements
.a-row {
  margin-bottom: 25px;
}

.a-col {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

// Ensure all input fields, selects, and buttons have the same width
.a-input,
.a-select,
.a-tree-select,
.a-button {
  width: 220px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

// Add consistent styling for labels
.a-col[style*="text-align: right"] {
  font-weight: bold;
  padding-right: 10px;
  color: #333;
  width: 120px;
}

// Button styles
.a-button {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.a-button:hover {
  background-color: #40a9ff;
  transform: translateY(-2px);
}

// Responsive adjustments
@media (max-width: 768px) {
  .a-col {
    flex-direction: column;
    align-items: flex-start;
  }

  .a-col[style*="text-align: right"] {
    text-align: left;
    padding-bottom: 5px;
    width: auto;
  }
}
</style>
