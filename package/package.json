{"name": "smart-xdc-engineer-tool", "version": "2.3.3", "private": true, "main": "./main.js", "homepage": "./", "scripts": {"build": "vue-cli-service build", "serve": "vue-cli-service serve", "lint": "vue-cli-service lint", "clear": "rimraf node_modules&&npm install --registry=https://registry.npmmirror.com", "use:npm": "nrm use npm", "use:taobao": "nrm use taobao", "update": "ncu -u --target greatest&&npm install --registry=https://registry.npmmirror.com", "deploy": "start ./deploy.sh", "electron": "node_modules\\.bin\\electron src\\main_electron.js http://localhost:9999", "package": "electron-packager . ElectronDemo --platform=win32 --arch=x64 --icon=computer.ico --out=./out --app-version=0.0.1 --overwrite --ignore=node_modules", "app:install": "npm i", "app:dist": "electron-builder", "build:win": "electron-builder --win", "build:win-portable": "electron-builder --win --config.portable"}, "build": {"productName": "SmartXDCEnginnerTool", "electronDownload": {"mirror": "https://npmmirror.com/mirrors/electron/"}, "asarUnpack": ["**/*.node"], "directories": {"output": "out"}, "appId": "com.smartxdc.configtool", "copyright": "LKSoft", "compression": "store", "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "build/icon.ico"}, "nsis": {"oneClick": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "工程配置工具", "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": true}, "npmRebuild": false, "buildDependenciesFromSource": true}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/x6": "^1.31.4", "@antv/x6-vue-shape": "^1.3.2", "ant-design-vue": "^3.2.0", "axios": "^0.21.1", "bcryptjs": "^2.4.3", "clipboard": "^2.0.8", "compression-webpack-plugin": "^5.0.1", "core-js": "^3.15.2", "crc": "^4.1.0", "dayjs": "^1.10.7", "exceljs": "^4.4.0", "express": "^4.21.2", "file-saver": "^2.0.5", "http-proxy-middleware": "^3.0.3", "image-webpack-loader": "^7.0.1", "ip": "^1.1.5", "js-cookie": "^3.0.0-rc.3", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "mockjs": "^1.1.0", "net-snmp": "^3.6.1", "parse": "^3.4.1", "qs": "^6.12.1", "remixicon": "^2.5.0", "serialport": "^10.3.0", "uuid": "^8.3.2", "vconsole": "^3.14.6", "vue": "^3.1.4", "vue-demi": "^0.14.10", "vue-router": "^4.0.10", "vue3-file-selector": "^1.0.3", "vuex": "^4.0.2", "vuex-persistedstate": "^4.1.0", "webpackbar": "^5.0.0-3", "xlsx": "^0.18.4"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.9", "@vue/cli-service": "^4.5.19", "@vue/compiler-sfc": "^3.1.4", "ansi-styles": "^6.2.1", "body-parser": "^1.19.0", "chalk": "^4.1.1", "chokidar": "^3.5.2", "electron": "^17.0.1", "electron-builder": "^26.0.12", "filemanager-webpack-plugin": "^6.1.4", "image-webpack-loader": "^7.0.1", "less": "^4.1.1", "less-loader": "^6.2.0", "prettier": "^2.3.2", "style-loader": "^3.3.1", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-recess-order": "^2.4.0", "svg-sprite-loader": "^6.0.9", "webpack": "^5.98.0", "webpackbar": "^5.0.0-3", "vue-loader": "^17.3.1"}}